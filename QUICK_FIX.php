<?php
/**
 * QUICK FIX for Variation Images
 * This will apply both CSS and JavaScript fixes
 */

// Load WordPress
require_once('wp-config.php');

echo '<h1>🚀 QUICK FIX - Variation Images</h1>';

// CSS Fix
$css_fix = '
/* RIDCOD Variation Images Quick Fix */
.variation-option.color-option.has-image .color-swatch {
    display: none !important;
    visibility: hidden !important;
    opacity: 0 !important;
}

.variation-option.color-option.has-image .variation-image-wrapper {
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
    position: absolute !important;
    top: 0 !important;
    left: 0 !important;
    right: 0 !important;
    bottom: 0 !important;
    z-index: 100 !important;
    border-radius: inherit !important;
    overflow: hidden !important;
}

.variation-option.color-option.has-image .variation-image {
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
    width: 100% !important;
    height: 100% !important;
    object-fit: cover !important;
    object-position: center !important;
    border-radius: inherit !important;
}

.variation-option.color-option.has-image {
    position: relative !important;
    overflow: hidden !important;
}
';

// JavaScript Fix
$js_fix = '
jQuery(document).ready(function($) {
    console.log("RIDCOD: Quick fix applied!");
    
    function fixVariationImages() {
        $(".variation-option.color-option.has-image .color-swatch").hide();
        $(".variation-option.color-option.has-image .variation-image-wrapper").show();
        console.log("RIDCOD: Fixed " + $(".variation-option.color-option.has-image").length + " variation images");
    }
    
    fixVariationImages();
    setTimeout(fixVariationImages, 500);
    setTimeout(fixVariationImages, 1000);
    
    $(document).on("found_variation ridcod_variations_updated", function() {
        setTimeout(fixVariationImages, 100);
    });
});
';

// Apply CSS Fix
$custom_css = wp_get_custom_css();
if (strpos($custom_css, 'RIDCOD Variation Images Quick Fix') === false) {
    wp_update_custom_css_post($custom_css . "\n\n" . $css_fix);
    echo '<p style="color: green;">✅ CSS Fix Applied</p>';
} else {
    echo '<p style="color: orange;">⚠️ CSS Fix Already Applied</p>';
}

// Apply JavaScript Fix
$theme_dir = get_stylesheet_directory();
$js_file = $theme_dir . '/ridcod-quick-fix.js';
file_put_contents($js_file, $js_fix);

$functions_file = $theme_dir . '/functions.php';
$function_code = "
// RIDCOD Quick Fix
function ridcod_quick_fix_js() {
    if (is_product()) {
        wp_enqueue_script('ridcod-quick-fix', get_stylesheet_directory_uri() . '/ridcod-quick-fix.js', array('jquery'), '1.0', true);
    }
}
add_action('wp_enqueue_scripts', 'ridcod_quick_fix_js');
";

if (is_writable($functions_file)) {
    $current_functions = file_get_contents($functions_file);
    if (strpos($current_functions, 'ridcod_quick_fix_js') === false) {
        file_put_contents($functions_file, $current_functions . $function_code);
        echo '<p style="color: green;">✅ JavaScript Fix Applied</p>';
    } else {
        echo '<p style="color: orange;">⚠️ JavaScript Fix Already Applied</p>';
    }
} else {
    echo '<p style="color: red;">❌ Cannot write to functions.php</p>';
}

echo '<div style="background: #d4edda; border: 1px solid #c3e6cb; padding: 20px; margin: 20px 0; border-radius: 5px;">';
echo '<h2>🎉 Quick Fix Applied Successfully!</h2>';
echo '<h3>What was done:</h3>';
echo '<ul>';
echo '<li>✅ Added CSS to hide color swatches when images are present</li>';
echo '<li>✅ Added JavaScript to ensure images are visible</li>';
echo '<li>✅ Applied fixes to WordPress Customizer and theme files</li>';
echo '</ul>';

echo '<h3>Next Steps:</h3>';
echo '<ol>';
echo '<li><strong>Clear all caches</strong> (WordPress cache, browser cache, CDN cache)</li>';
echo '<li><strong>Hard refresh</strong> your browser (Ctrl+F5 or Cmd+Shift+R)</li>';
echo '<li><strong>Visit your product page</strong> and check if images now show instead of color circles</li>';
echo '<li><strong>Open browser console</strong> (F12) and look for "RIDCOD: Quick fix applied!" message</li>';
echo '</ol>';
echo '</div>';

echo '<div style="background: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; margin: 20px 0; border-radius: 5px;">';
echo '<h3>⚠️ If images still don\'t show:</h3>';
echo '<ol>';
echo '<li>Open browser developer tools (F12)</li>';
echo '<li>Go to Console tab</li>';
echo '<li>Look for any JavaScript errors</li>';
echo '<li>Try this manual command in console: <code>jQuery(".variation-option.color-option.has-image .color-swatch").hide()</code></li>';
echo '<li>Check if the images are actually loading in Network tab</li>';
echo '</ol>';
echo '</div>';

echo '<h3>Manual CSS (if needed):</h3>';
echo '<p>If the automatic fix doesn\'t work, manually add this CSS to Appearance > Customize > Additional CSS:</p>';
echo '<textarea style="width: 100%; height: 150px; font-family: monospace;">' . esc_textarea($css_fix) . '</textarea>';

echo '<h3>Manual JavaScript (if needed):</h3>';
echo '<p>Add this to your theme\'s footer or product page:</p>';
echo '<textarea style="width: 100%; height: 100px; font-family: monospace;"><script>' . esc_textarea($js_fix) . '</script></textarea>';

echo '<hr>';
echo '<p><strong>Test URL:</strong> <a href="' . home_url() . '" target="_blank">Visit your site</a></p>';
?>
