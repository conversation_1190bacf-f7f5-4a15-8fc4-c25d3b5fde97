<?php
/**
 * Test script for variation images feature
 * This will enable the feature and show test results
 */

// Load WordPress
require_once('wp-config.php');

echo '<h1>RIDCOD Variation Images Test</h1>';

// Enable the feature
update_option('rid_cod_show_variation_images', 'yes');
echo '<p style="color: green;">✅ Variation Images feature has been ENABLED!</p>';

// Get a variable product for testing
$products = get_posts(array(
    'post_type' => 'product',
    'meta_query' => array(
        array(
            'key' => '_product_type',
            'value' => 'variable'
        )
    ),
    'posts_per_page' => 1
));

if (empty($products)) {
    echo '<p style="color: red;">❌ No variable products found. Please create a variable product first.</p>';
    exit;
}

$product_id = $products[0]->ID;
$product = wc_get_product($product_id);

echo '<h2>Testing with Product: ' . $product->get_name() . ' (ID: ' . $product_id . ')</h2>';

// Test the variation images function
require_once(ABSPATH . 'wp-content/plugins/RIDCOD/includes/class-rid-cod-form.php');

$form = new RID_COD_Form();
$reflection = new ReflectionClass($form);
$method = $reflection->getMethod('get_variation_images');
$method->setAccessible(true);

$variation_images = $method->invoke($form, $product);

echo '<h3>Extracted Variation Images:</h3>';
if (empty($variation_images)) {
    echo '<p style="color: orange;">⚠️ No variation images found.</p>';
    
    // Show what we have
    $variations = $product->get_available_variations();
    echo '<h4>Available Variations:</h4>';
    echo '<table border="1" cellpadding="5">';
    echo '<tr><th>Variation ID</th><th>Attributes</th><th>Image</th></tr>';
    
    foreach ($variations as $variation) {
        echo '<tr>';
        echo '<td>' . $variation['variation_id'] . '</td>';
        echo '<td>';
        foreach ($variation['attributes'] as $attr => $value) {
            echo $attr . ': ' . $value . '<br>';
            echo 'Decoded: ' . urldecode($value) . '<br>';
        }
        echo '</td>';
        echo '<td>' . (isset($variation['image']['src']) ? 'Has Image' : 'No Image') . '</td>';
        echo '</tr>';
    }
    echo '</table>';
    
} else {
    echo '<p style="color: green;">✅ Found ' . count($variation_images) . ' variation images!</p>';
    
    echo '<table border="1" cellpadding="5">';
    echo '<tr><th>Key</th><th>Image</th><th>Variation ID</th></tr>';
    
    foreach ($variation_images as $key => $image_data) {
        echo '<tr>';
        echo '<td>' . esc_html($key) . '</td>';
        echo '<td><img src="' . esc_url($image_data['src']) . '" style="max-width: 100px; max-height: 100px;"></td>';
        echo '<td>' . $image_data['variation_id'] . '</td>';
        echo '</tr>';
    }
    echo '</table>';
}

// Test URL encoding/decoding
echo '<h3>URL Encoding Test:</h3>';
$test_values = array('أحمر', 'أزرق', 'أخضر', 'black', 'red', 'blue');
echo '<table border="1" cellpadding="5">';
echo '<tr><th>Original</th><th>URL Encoded</th><th>URL Decoded</th><th>RID Decoded</th></tr>';

foreach ($test_values as $value) {
    $encoded = urlencode($value);
    $url_decoded = urldecode($encoded);
    $rid_decoded = rid_cod_decode_variation_attribute($encoded);
    
    echo '<tr>';
    echo '<td>' . $value . '</td>';
    echo '<td>' . $encoded . '</td>';
    echo '<td>' . $url_decoded . '</td>';
    echo '<td>' . $rid_decoded . '</td>';
    echo '</tr>';
}
echo '</table>';

// Show current settings
echo '<h3>Current Settings:</h3>';
$settings = array(
    'rid_cod_show_variation_images' => get_option('rid_cod_show_variation_images', 'no'),
    'rid_cod_show_color_names' => get_option('rid_cod_show_color_names', 'no'),
    'rid_cod_use_dropdown_variations' => get_option('rid_cod_use_dropdown_variations', 'no'),
    'rid_cod_form_style' => get_option('rid_cod_form_style', 'classic')
);

echo '<table border="1" cellpadding="5">';
echo '<tr><th>Setting</th><th>Value</th></tr>';
foreach ($settings as $key => $value) {
    echo '<tr><td>' . $key . '</td><td>' . $value . '</td></tr>';
}
echo '</table>';

echo '<hr>';
echo '<h3>Next Steps:</h3>';
echo '<ol>';
echo '<li>Go to your product page: <a href="' . get_permalink($product_id) . '" target="_blank">' . get_permalink($product_id) . '</a></li>';
echo '<li>Check if images appear instead of color swatches</li>';
echo '<li>If not working, check the HTML source for debug comments</li>';
echo '<li>Use <a href="debug-variation-images.php?product_id=' . $product_id . '">detailed debug</a> for more info</li>';
echo '</ol>';

echo '<hr>';
echo '<h3>Recent Error Logs (last 20 lines):</h3>';
$log_file = WP_CONTENT_DIR . '/debug.log';
if (file_exists($log_file)) {
    $logs = file($log_file);
    $recent_logs = array_slice($logs, -20);
    echo '<pre style="background: #f0f0f0; padding: 10px; max-height: 300px; overflow-y: scroll;">';
    foreach ($recent_logs as $log) {
        if (strpos($log, 'RIDCOD') !== false) {
            echo esc_html($log);
        }
    }
    echo '</pre>';
} else {
    echo '<p>Debug log file not found. Make sure WP_DEBUG_LOG is enabled.</p>';
}

echo '<hr>';
echo '<p><strong>Tip:</strong> Make sure each variation has a custom image set in WooCommerce product settings!</p>';
?>
