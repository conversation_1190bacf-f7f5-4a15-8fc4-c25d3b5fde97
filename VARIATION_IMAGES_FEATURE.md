# ميزة عرض صور المنتج في متغيرات اللون

## نظرة عامة

تم إضافة ميزة جديدة لإضافة RIDCOD - Ecom تتيح عرض صور المنتج في متغيرات اللون بدلاً من الدوائر الملونة أو النصوص. هذه الميزة تحسن تجربة المستخدم وتجعل اختيار الألوان أكثر وضوحاً ودقة.

## الميزات الرئيسية

### 1. عرض صور المتغيرات
- عرض صور المنتج المرتبطة بكل متغير لون
- دعم جميع تنسيقات الصور المدعومة في WooCommerce
- تحميل تدريجي للصور (lazy loading)
- دعم srcset و sizes للصور المتجاوبة

### 2. التفاعل التلقائي
- تحديث الصورة الرئيسية للمنتج عند اختيار لون
- تحديث السعر والمخزون تلقائياً
- تأثيرات بصرية سلسة عند التبديل بين الألوان
- توافق كامل مع وظائف WooCommerce الافتراضية

### 3. النظام الاحتياطي
- عرض دائرة لون عند عدم وجود صورة للمتغير
- عرض اسم اللون كنص احتياطي
- تصميم محسن للعناصر الاحتياطية

### 4. التصميم المتجاوب
- تصميم دائري أو مربع للصور
- أحجام متعددة (صغير، متوسط، كبير، كبير جداً)
- تكيف تلقائي مع أحجام الشاشات المختلفة
- تأثيرات hover وselection محسنة

## إعدادات الميزة

### تفعيل الميزة
1. انتقل إلى **WooCommerce > RIDCOD - Ecom**
2. اختر تبويبة **"إعدادات التحكم في النموذج"**
3. فعل خيار **"عرض صور المنتج في متغيرات اللون"**
4. احفظ الإعدادات

### الإعدادات المرتبطة
- **حجم متغيرات المنتج**: يؤثر على حجم صور المتغيرات
- **إظهار أسماء الألوان**: يستخدم كنظام احتياطي عند عدم وجود صور
- **تحويل متغيرات المنتج إلى قوائم منسدلة**: يخفي صور المتغيرات عند التفعيل

## متطلبات الاستخدام

### إعداد المنتجات
1. أنشئ منتج متغير في WooCommerce
2. أضف خاصية "اللون" أو "Color" للمنتج
3. لكل متغير لون، أضف صورة مخصصة في إعدادات المتغير
4. احفظ المنتج

### أسماء الخصائص المدعومة
الميزة تتعرف تلقائياً على خصائص الألوان بالأسماء التالية:
- `color`, `colour` (إنجليزي)
- `لون`, `اللون` (عربي)
- `pa_color`, `pa_colour`, `pa_لون`, `pa_اللون` (خصائص WooCommerce)

## الملفات المحدثة

### PHP
- `includes/class-rid-cod-customizer.php`: إضافة إعداد جديد
- `includes/class-rid-cod-form.php`: دالة استخراج صور المتغيرات وتحديث HTML

### JavaScript
- `assets/js/rid-cod-variations.js`: دعم التفاعل مع صور المتغيرات

### CSS
- `assets/css/rid-cod.css`: تصميم صور المتغيرات والتأثيرات البصرية

## الأحداث المخصصة (Custom Events)

### `ridcod_variation_image_selected`
يتم إطلاقه عند اختيار متغير بصورة:
```javascript
$(document).on('ridcod_variation_image_selected', function(event, data) {
    console.log('Variation selected:', data.variationId);
    console.log('Attribute:', data.attributeName);
    console.log('Value:', data.attributeValue);
});
```

## التوافق

### WooCommerce
- متوافق مع WooCommerce 3.0+
- يحافظ على جميع وظائف WooCommerce الافتراضية
- لا يتداخل مع إضافات WooCommerce الأخرى

### المتصفحات
- جميع المتصفحات الحديثة
- دعم Internet Explorer 11+
- تصميم متجاوب للأجهزة المحمولة

## استكشاف الأخطاء

### الصور لا تظهر
1. تأكد من تفعيل الميزة في الإعدادات
2. تأكد من وجود صور للمتغيرات في WooCommerce
3. تأكد من أن اسم خاصية اللون مدعوم

### الصور لا تتحدث في المعرض الرئيسي
1. تأكد من وجود عنصر الصورة الرئيسية في الصفحة
2. تحقق من console المتصفح للأخطاء

### مشاكل في التصميم
1. امسح cache المتصفح
2. تأكد من تحديث ملفات CSS
3. تحقق من عدم وجود تعارض مع CSS أخرى

## الدعم الفني

للحصول على الدعم الفني أو الإبلاغ عن مشاكل، يرجى التواصل مع فريق تطوير RIDCOD - Ecom.

---

**تاريخ الإضافة**: 2025-08-08  
**الإصدار**: 1.0  
**المطور**: RIDCOD Team
