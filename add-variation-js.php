<?php
/**
 * Add variation images JavaScript fix
 * Run this to add the JS fix to your WordPress
 */

// Load WordPress
require_once('wp-config.php');

echo '<h1>Add Variation Images JavaScript Fix</h1>';

// Read the JS fix file
$js_file = __DIR__ . '/fix-variation-images.js';
if (!file_exists($js_file)) {
    echo '<p style="color: red;">❌ JavaScript fix file not found!</p>';
    exit;
}

$js_content = file_get_contents($js_file);

// Add JavaScript to WordPress footer
function ridcod_variation_images_fix() {
    global $js_content;
    if (is_product() || is_shop() || is_product_category()) {
        echo '<script type="text/javascript">' . $js_content . '</script>';
    }
}

// Try to add the function to WordPress
$functions_file = get_stylesheet_directory() . '/functions.php';
$function_code = "
// RIDCOD Variation Images Fix
function ridcod_variation_images_fix() {
    if (is_product() || is_shop() || is_product_category()) {
        wp_enqueue_script('ridcod-variation-fix', get_stylesheet_directory_uri() . '/ridcod-variation-fix.js', array('jquery'), '1.0', true);
    }
}
add_action('wp_enqueue_scripts', 'ridcod_variation_images_fix');
";

// Copy JS file to theme directory
$theme_js_file = get_stylesheet_directory() . '/ridcod-variation-fix.js';
$js_copied = file_put_contents($theme_js_file, $js_content);

if ($js_copied) {
    echo '<p style="color: green;">✅ JavaScript file copied to theme directory</p>';
    
    // Add function to functions.php if not already there
    if (is_writable($functions_file)) {
        $current_functions = file_get_contents($functions_file);
        if (strpos($current_functions, 'ridcod_variation_images_fix') === false) {
            file_put_contents($functions_file, $current_functions . $function_code);
            echo '<p style="color: green;">✅ Function added to functions.php</p>';
        } else {
            echo '<p style="color: orange;">⚠️ Function already exists in functions.php</p>';
        }
    } else {
        echo '<p style="color: orange;">⚠️ Cannot write to functions.php</p>';
        echo '<p>Please manually add this code to your theme\'s functions.php:</p>';
        echo '<textarea style="width: 100%; height: 150px; font-family: monospace;">' . esc_textarea($function_code) . '</textarea>';
    }
} else {
    echo '<p style="color: red;">❌ Could not copy JavaScript file</p>';
}

// Alternative: Add inline script
echo '<h3>Alternative: Inline Script</h3>';
echo '<p>If the above method doesn\'t work, you can add this script directly to your product pages:</p>';
echo '<textarea style="width: 100%; height: 200px; font-family: monospace;">';
echo '<script type="text/javascript">' . esc_textarea($js_content) . '</script>';
echo '</textarea>';

echo '<div style="background: #d4edda; border: 1px solid #c3e6cb; padding: 15px; margin: 20px 0; border-radius: 5px;">';
echo '<h3>✅ JavaScript Fix Applied!</h3>';
echo '<p>The variation images should now be visible. Please:</p>';
echo '<ol>';
echo '<li>Clear any caching plugins</li>';
echo '<li>Hard refresh your browser (Ctrl+F5 or Cmd+Shift+R)</li>';
echo '<li>Check your product page again</li>';
echo '<li>Open browser developer tools (F12) and check the Console for RIDCOD messages</li>';
echo '</ol>';
echo '</div>';

echo '<h3>Troubleshooting:</h3>';
echo '<ul>';
echo '<li>Open browser developer tools (F12)</li>';
echo '<li>Go to Console tab</li>';
echo '<li>Look for "RIDCOD: Fixing variation images display..." messages</li>';
echo '<li>If you see errors, please report them</li>';
echo '</ul>';

echo '<h3>Manual Test:</h3>';
echo '<p>You can also test this manually by opening browser console and running:</p>';
echo '<code>jQuery(\'.variation-option.color-option.has-image .color-swatch\').hide();</code>';

echo '<hr>';
echo '<h3>Quick Links:</h3>';
echo '<ul>';
echo '<li><a href="' . admin_url('theme-editor.php?file=functions.php') . '" target="_blank">Edit functions.php</a></li>';
echo '<li><a href="add-variation-css.php">Add CSS Fix</a></li>';
echo '</ul>';
?>
