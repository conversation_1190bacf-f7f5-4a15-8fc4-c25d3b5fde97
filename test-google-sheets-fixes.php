<?php
/**
 * Test file for Google Sheets fixes
 * This file can be used to test the Google Sheets integration fixes
 * 
 * Usage: Place this file in the plugin root and access it via browser
 * Example: http://yoursite.com/wp-content/plugins/RIDCOD/test-google-sheets-fixes.php
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    // Load WordPress
    require_once('../../../wp-load.php');
}

// Check if user has admin privileges
if (!current_user_can('manage_options')) {
    wp_die('Access denied. Admin privileges required.');
}

?>
<!DOCTYPE html>
<html>
<head>
    <title>RIDCOD - Google Sheets Fixes Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; direction: rtl; }
        .test-section { background: #f9f9f9; padding: 15px; margin: 10px 0; border-radius: 5px; }
        .success { color: green; }
        .error { color: red; }
        .info { color: blue; }
        .test-data { background: #fff; padding: 10px; border: 1px solid #ddd; margin: 10px 0; }
        button { padding: 10px 20px; margin: 5px; }
    </style>
</head>
<body>
    <h1>اختبار إصلاحات Google Sheets - RIDCOD</h1>
    
    <div class="test-section">
        <h2>1. اختبار تنسيق رقم الهاتف</h2>
        <p>هذا الاختبار يتحقق من أن رقم الهاتف يتم تنسيقه بشكل صحيح للحفاظ على الصفر في البداية.</p>
        
        <?php
        // Test phone number formatting
        $test_phone = '0123456789';
        $formatted_phone = "'" . $test_phone;
        
        echo "<div class='test-data'>";
        echo "<strong>رقم الهاتف الأصلي:</strong> " . $test_phone . "<br>";
        echo "<strong>رقم الهاتف المنسق:</strong> " . $formatted_phone . "<br>";
        echo "<span class='success'>✅ تم تطبيق التنسيق بنجاح</span>";
        echo "</div>";
        ?>
    </div>
    
    <div class="test-section">
        <h2>2. اختبار بيانات Google Sheets</h2>
        <p>هذا الاختبار يعرض البيانات التي سيتم إرسالها إلى Google Sheets.</p>
        
        <?php
        // Test data structure
        $test_data = array(
            'product_id'    => 123,
            'product_name'  => 'منتج تجريبي',
            'quantity'      => 1,
            'color'         => 'أحمر',
            'size'          => 'كبير',
            'width'         => 'عادي',
            'customer_name' => 'عميل تجريبي',
            'phone'         => "'0123456789", // مع التنسيق الجديد
            'state'         => 'الجزائر',
            'city'          => 'الجزائر العاصمة',
            'delivery_type' => 'توصيل للمنزل',
            'notes'         => 'ملاحظات تجريبية - تم إضافة هذا الحقل الجديد',
            'product_price' => '1000 دج',
            'shipping_cost' => '300 دج',
            'total_price'   => '1300 دج',
        );
        
        echo "<div class='test-data'>";
        echo "<strong>بيانات الاختبار:</strong><br>";
        foreach ($test_data as $key => $value) {
            $label = '';
            switch ($key) {
                case 'product_id': $label = 'معرف المنتج'; break;
                case 'product_name': $label = 'اسم المنتج'; break;
                case 'quantity': $label = 'الكمية'; break;
                case 'color': $label = 'اللون'; break;
                case 'size': $label = 'القياس'; break;
                case 'width': $label = 'العرض'; break;
                case 'customer_name': $label = 'اسم العميل'; break;
                case 'phone': $label = 'الهاتف'; break;
                case 'state': $label = 'الولاية'; break;
                case 'city': $label = 'البلدية'; break;
                case 'delivery_type': $label = 'نوع التوصيل'; break;
                case 'notes': $label = 'الملاحظات'; break;
                case 'product_price': $label = 'سعر المنتج'; break;
                case 'shipping_cost': $label = 'تكلفة الشحن'; break;
                case 'total_price': $label = 'السعر الإجمالي'; break;
            }
            
            $highlight = '';
            if ($key === 'phone') {
                $highlight = ' style="background-color: #ffffcc;"'; // تمييز رقم الهاتف
            } elseif ($key === 'notes') {
                $highlight = ' style="background-color: #ccffcc;"'; // تمييز الملاحظات الجديدة
            }
            
            echo "<div{$highlight}><strong>{$label}:</strong> {$value}</div>";
        }
        echo "</div>";
        ?>
    </div>
    
    <div class="test-section">
        <h2>3. اختبار Google Apps Script Headers</h2>
        <p>هذا الاختبار يعرض العناوين الجديدة التي يجب استخدامها في Google Apps Script.</p>
        
        <?php
        $headers = [
            "معرف المنتج", "اسم المنتج", "الكمية", "اللون", "القياس", "العرض",
            "اسم العميل", "الهاتف", "الولاية", "البلدية", "نوع التوصيل", "الملاحظات",
            "سعر المنتج", "تكلفة الشحن", "السعر الإجمالي", "تاريخ الطلب"
        ];
        
        $data_keys = [
            'product_id', 'product_name', 'quantity', 'color', 'size', 'width',
            'customer_name', 'phone', 'state', 'city', 'delivery_type', 'notes',
            'product_price', 'shipping_cost', 'total_price', 'order_date'
        ];
        
        echo "<div class='test-data'>";
        echo "<strong>العناوين العربية (HEADERS_AR):</strong><br>";
        echo "['" . implode("', '", $headers) . "']<br><br>";
        
        echo "<strong>مفاتيح البيانات (DATA_KEYS):</strong><br>";
        echo "['" . implode("', '", $data_keys) . "']<br>";
        
        echo "<br><span class='info'>📝 ملاحظة: تم إضافة 'الملاحظات' و 'notes' إلى القوائم</span>";
        echo "</div>";
        ?>
    </div>
    
    <div class="test-section">
        <h2>4. اختبار إرسال بيانات تجريبية</h2>
        <p>يمكنك استخدام هذا الزر لإرسال بيانات تجريبية إلى Google Sheets (إذا كان مفعلاً).</p>
        
        <button onclick="testGoogleSheets()" id="testBtn">إرسال بيانات تجريبية</button>
        <div id="testResult"></div>
        
        <script>
        function testGoogleSheets() {
            const btn = document.getElementById('testBtn');
            const result = document.getElementById('testResult');
            
            btn.disabled = true;
            btn.textContent = 'جاري الإرسال...';
            result.innerHTML = '<div class="info">جاري إرسال البيانات التجريبية...</div>';
            
            // استخدام AJAX لإرسال البيانات التجريبية
            fetch('<?php echo admin_url('admin-ajax.php'); ?>', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: 'action=rid_cod_test_google_sheets_send&nonce=<?php echo wp_create_nonce('rid_cod_nonce'); ?>'
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    result.innerHTML = '<div class="success">✅ تم إرسال البيانات بنجاح!</div>';
                } else {
                    result.innerHTML = '<div class="error">❌ فشل في الإرسال: ' + (data.data ? data.data.message : 'خطأ غير معروف') + '</div>';
                }
            })
            .catch(error => {
                result.innerHTML = '<div class="error">❌ خطأ في الشبكة: ' + error.message + '</div>';
            })
            .finally(() => {
                btn.disabled = false;
                btn.textContent = 'إرسال بيانات تجريبية';
            });
        }
        </script>
    </div>
    
    <div class="test-section">
        <h2>5. ملخص الإصلاحات</h2>
        <div class="test-data">
            <h3>المشاكل التي تم حلها:</h3>
            <ul>
                <li><span class="success">✅</span> <strong>رقم الهاتف:</strong> تم إضافة علامة اقتباس (') لحفظ الصفر في البداية</li>
                <li><span class="success">✅</span> <strong>الملاحظات:</strong> تم إضافة حقل الملاحظات إلى Google Sheets</li>
                <li><span class="success">✅</span> <strong>الولايات النصية:</strong> تم إصلاح مشكلة الحقول النصية للولايات</li>
                <li><span class="success">✅</span> <strong>Google Apps Script:</strong> تم تحديث الكود لدعم الحقول الجديدة</li>
            </ul>
            
            <h3>الملفات المعدلة:</h3>
            <ul>
                <li><code>includes/class-rid-cod-ajax.php</code></li>
                <li><code>includes/class-rid-cod-google-sheets-settings.php</code></li>
                <li><code>assets/js/rid-cod.js</code></li>
            </ul>
        </div>
    </div>
    
    <p><a href="<?php echo admin_url('admin.php?page=rid_cod_settings'); ?>">← العودة إلى إعدادات RIDCOD</a></p>
</body>
</html>
