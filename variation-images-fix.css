/* 
 * RIDCOD Variation Images Fix
 * Add this CSS to ensure variation images show properly
 */

/* Force hide color swatches when images are present */
.rid-cod-variations .variation-option.color-option.has-image .color-swatch,
.rid-cod-form-third .rid-cod-variations-external .variation-option.color-option.has-image .color-swatch {
    display: none !important;
    visibility: hidden !important;
    opacity: 0 !important;
}

/* Force show variation images */
.rid-cod-variations .variation-option.color-option.has-image .variation-image-wrapper,
.rid-cod-form-third .rid-cod-variations-external .variation-option.color-option.has-image .variation-image-wrapper {
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
    position: absolute !important;
    top: 0 !important;
    left: 0 !important;
    right: 0 !important;
    bottom: 0 !important;
    z-index: 100 !important;
    border-radius: inherit !important;
    overflow: hidden !important;
}

.rid-cod-variations .variation-option.color-option.has-image .variation-image,
.rid-cod-form-third .rid-cod-variations-external .variation-option.color-option.has-image .variation-image {
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
    width: 100% !important;
    height: 100% !important;
    object-fit: cover !important;
    object-position: center !important;
    border-radius: inherit !important;
    max-width: none !important;
    max-height: none !important;
}

/* Ensure the variation option has proper positioning */
.rid-cod-variations .variation-option.color-option.has-image,
.rid-cod-form-third .rid-cod-variations-external .variation-option.color-option.has-image {
    position: relative !important;
    overflow: hidden !important;
    background: transparent !important;
}

/* Debug: Add a border to see if the wrapper is there */
.variation-image-wrapper {
    border: 2px solid red !important;
}

/* Remove debug border after confirming it works */
/*
.variation-image-wrapper {
    border: none !important;
}
*/

/* Additional specificity for stubborn themes */
body .rid-cod-variations .variation-option.color-option.has-image .color-swatch,
body .rid-cod-form-third .rid-cod-variations-external .variation-option.color-option.has-image .color-swatch {
    display: none !important;
}

body .rid-cod-variations .variation-option.color-option.has-image .variation-image-wrapper,
body .rid-cod-form-third .rid-cod-variations-external .variation-option.color-option.has-image .variation-image-wrapper {
    display: block !important;
    position: absolute !important;
    top: 0 !important;
    left: 0 !important;
    right: 0 !important;
    bottom: 0 !important;
    z-index: 999 !important;
}

body .rid-cod-variations .variation-option.color-option.has-image .variation-image,
body .rid-cod-form-third .rid-cod-variations-external .variation-option.color-option.has-image .variation-image {
    display: block !important;
    width: 100% !important;
    height: 100% !important;
    object-fit: cover !important;
}
