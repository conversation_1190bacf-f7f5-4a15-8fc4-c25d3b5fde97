# دليل الإعداد السريع لميزة صور المتغيرات

## الخطوات السريعة للتفعيل

### 1. تفعيل الميزة
```
1. انتقل إلى: WooCommerce > RIDCOD - Ecom
2. اختر تبويبة: "إعدادات التحكم في النموذج"
3. فعل: "عرض صور المنتج في متغيرات اللون"
4. احفظ الإعدادات
```

### 2. إعداد المنتج
```
1. انتقل إلى منتج متغير في WooCommerce
2. تأكد من وجود خاصية "اللون" أو "Color"
3. لكل متغير، أضف صورة مخصصة في "Variation Image"
4. احفظ المنتج
```

### 3. التحقق من العمل
```
1. انتقل إلى صفحة المنتج
2. يجب أن تظهر صور بدلاً من الدوائر الملونة
3. عند النقر على صورة، تتغير الصورة الرئيسية
```

## استكشاف الأخطاء السريع

### المشكلة: الصور لا تظهر
**الحلول:**
1. تأكد من تفعيل الميزة في الإعدادات
2. تأكد من أن المنتج متغير وليس بسيط
3. تأكد من وجود خاصية لون (color, colour, لون, اللون)
4. تأكد من إضافة صور للمتغيرات في WooCommerce

### المشكلة: تظهر دوائر ملونة بدلاً من الصور
**الحلول:**
1. تحقق من أن كل متغير له صورة مخصصة
2. تأكد من أن اسم الخاصية يحتوي على كلمة "لون" أو "color"
3. جرب إعادة حفظ المنتج

### المشكلة: الصورة الرئيسية لا تتغير
**الحلول:**
1. تأكد من وجود صورة رئيسية للمنتج
2. امسح cache المتصفح
3. تحقق من console المتصفح للأخطاء

## ملفات التشخيص

### للتشخيص المتقدم:
1. `debug-variation-images.php` - لتشخيص استخراج الصور
2. `enable-variation-images.php` - لتفعيل/تعطيل الميزة بسرعة

### استخدام ملفات التشخيص:
```
1. ارفع الملف إلى جذر WordPress
2. انتقل إلى: yoursite.com/debug-variation-images.php
3. اتبع التعليمات في الصفحة
```

## أسماء الخصائص المدعومة

الميزة تتعرف على هذه الأسماء تلقائياً:
- `color` (إنجليزي)
- `colour` (إنجليزي بريطاني)
- `لون` (عربي)
- `اللون` (عربي مع أل التعريف)
- `pa_color` (خاصية WooCommerce)
- `pa_colour` (خاصية WooCommerce)
- `pa_لون` (خاصية WooCommerce عربي)
- `pa_اللون` (خاصية WooCommerce عربي)

## نصائح مهمة

1. **حجم الصور**: استخدم صور بحجم 300x300 بكسل للحصول على أفضل أداء
2. **تنسيق الصور**: JPG أو PNG مدعومان
3. **عدد المتغيرات**: الميزة تعمل مع أي عدد من المتغيرات
4. **التوافق**: متوافقة مع جميع أشكال النماذج (كلاسيكي، ثاني، ثالث)

## الدعم

إذا واجهت مشاكل:
1. استخدم ملفات التشخيص أولاً
2. تحقق من error logs في WordPress
3. تواصل مع فريق RIDCOD للدعم الفني

---
**آخر تحديث**: 2025-08-08
