<?php
/**
 * Enable WordPress debug logging
 */

// Load WordPress
require_once('wp-config.php');

echo '<h1>Enable WordPress Debug Logging</h1>';

// Check current debug status
$wp_debug = defined('WP_DEBUG') && WP_DEBUG;
$wp_debug_log = defined('WP_DEBUG_LOG') && WP_DEBUG_LOG;

echo '<h2>Current Status:</h2>';
echo '<p><strong>WP_DEBUG:</strong> ' . ($wp_debug ? 'Enabled' : 'Disabled') . '</p>';
echo '<p><strong>WP_DEBUG_LOG:</strong> ' . ($wp_debug_log ? 'Enabled' : 'Disabled') . '</p>';

if (!$wp_debug || !$wp_debug_log) {
    echo '<div style="background: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; margin: 20px 0; border-radius: 5px;">';
    echo '<h3>⚠️ Debug Logging is Not Fully Enabled</h3>';
    echo '<p>To enable debug logging, add these lines to your wp-config.php file (before the "That\'s all, stop editing!" line):</p>';
    echo '<pre style="background: #f8f9fa; padding: 10px; border-radius: 3px;">';
    echo "define('WP_DEBUG', true);\n";
    echo "define('WP_DEBUG_LOG', true);\n";
    echo "define('WP_DEBUG_DISPLAY', false);\n";
    echo '</pre>';
    echo '</div>';
} else {
    echo '<div style="background: #d4edda; border: 1px solid #c3e6cb; padding: 15px; margin: 20px 0; border-radius: 5px;">';
    echo '<h3>✅ Debug Logging is Enabled</h3>';
    echo '<p>Debug logs will be written to: <code>' . WP_CONTENT_DIR . '/debug.log</code></p>';
    echo '</div>';
}

// Show recent RIDCOD logs if available
$log_file = WP_CONTENT_DIR . '/debug.log';
if (file_exists($log_file)) {
    echo '<h3>Recent RIDCOD Logs:</h3>';
    $logs = file($log_file);
    $ridcod_logs = array();
    
    // Get last 50 lines and filter for RIDCOD
    $recent_logs = array_slice($logs, -50);
    foreach ($recent_logs as $log) {
        if (stripos($log, 'RIDCOD') !== false) {
            $ridcod_logs[] = $log;
        }
    }
    
    if (!empty($ridcod_logs)) {
        echo '<pre style="background: #f8f9fa; padding: 10px; max-height: 400px; overflow-y: scroll; border: 1px solid #dee2e6; border-radius: 3px;">';
        foreach (array_slice($ridcod_logs, -20) as $log) {
            echo esc_html($log);
        }
        echo '</pre>';
    } else {
        echo '<p>No RIDCOD logs found in recent entries.</p>';
    }
} else {
    echo '<p>Debug log file does not exist yet.</p>';
}

echo '<hr>';
echo '<h3>Quick Actions:</h3>';
echo '<ul>';
echo '<li><a href="test-variation-images.php">Test Variation Images</a></li>';
echo '<li><a href="debug-variation-images.php">Debug Variation Images</a></li>';
echo '<li><a href="enable-variation-images.php">Enable/Disable Feature</a></li>';
echo '</ul>';

echo '<hr>';
echo '<h3>Instructions:</h3>';
echo '<ol>';
echo '<li>Enable debug logging using the code above</li>';
echo '<li>Visit a product page with the RIDCOD form</li>';
echo '<li>Come back to this page to see the debug logs</li>';
echo '<li>Look for messages about variation images processing</li>';
echo '</ol>';
?>
