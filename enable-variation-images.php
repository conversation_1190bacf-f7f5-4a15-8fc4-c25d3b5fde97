<?php
/**
 * Quick script to enable variation images feature
 * Add this to your WordPress root and access via browser
 */

// Load WordPress
require_once('wp-config.php');

echo '<h1>RIDCOD Variation Images Feature Control</h1>';

// Check current status
$current_status = get_option('rid_cod_show_variation_images', 'no');
echo '<h2>Current Status: ' . ($current_status === 'yes' ? 'ENABLED' : 'DISABLED') . '</h2>';

// Handle form submission
if (isset($_POST['action'])) {
    if ($_POST['action'] === 'enable') {
        update_option('rid_cod_show_variation_images', 'yes');
        echo '<p style="color: green; font-weight: bold;">✅ Variation Images feature has been ENABLED!</p>';
        $current_status = 'yes';
    } elseif ($_POST['action'] === 'disable') {
        update_option('rid_cod_show_variation_images', 'no');
        echo '<p style="color: red; font-weight: bold;">❌ Variation Images feature has been DISABLED!</p>';
        $current_status = 'no';
    }
}

// Show control form
echo '<form method="post">';
if ($current_status === 'yes') {
    echo '<button type="submit" name="action" value="disable" style="background: #dc3545; color: white; padding: 10px 20px; border: none; border-radius: 5px; font-size: 16px;">Disable Feature</button>';
} else {
    echo '<button type="submit" name="action" value="enable" style="background: #28a745; color: white; padding: 10px 20px; border: none; border-radius: 5px; font-size: 16px;">Enable Feature</button>';
}
echo '</form>';

echo '<hr>';

// Show other related settings
echo '<h3>Related Settings:</h3>';
$related_settings = array(
    'rid_cod_show_color_names' => 'Show Color Names',
    'rid_cod_use_dropdown_variations' => 'Use Dropdown Variations',
    'rid_cod_variation_size' => 'Variation Size',
    'rid_cod_form_style' => 'Form Style'
);

echo '<table border="1" cellpadding="5">';
echo '<tr><th>Setting</th><th>Value</th></tr>';
foreach ($related_settings as $setting => $label) {
    $value = get_option($setting, 'not set');
    echo '<tr><td>' . $label . ' (' . $setting . ')</td><td>' . $value . '</td></tr>';
}
echo '</table>';

echo '<hr>';
echo '<h3>Quick Links:</h3>';
echo '<ul>';
echo '<li><a href="debug-variation-images.php">Debug Variation Images</a></li>';
echo '<li><a href="' . admin_url('admin.php?page=rid-cod-settings&tab=form_control') . '">RIDCOD Settings (Form Control)</a></li>';
echo '</ul>';

echo '<hr>';
echo '<h3>Instructions:</h3>';
echo '<ol>';
echo '<li>Enable the feature using the button above</li>';
echo '<li>Make sure you have variable products with color attributes</li>';
echo '<li>Add images to each variation in WooCommerce product settings</li>';
echo '<li>The color attribute should be named: color, colour, لون, اللون, or similar</li>';
echo '<li>Test on a product page with RIDCOD form enabled</li>';
echo '</ol>';
?>
