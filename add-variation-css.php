<?php
/**
 * Add variation images CSS fix
 * Run this once to add the CSS fix to your theme
 */

// Load WordPress
require_once('wp-config.php');

echo '<h1>Add Variation Images CSS Fix</h1>';

// Read the CSS fix file
$css_file = __DIR__ . '/variation-images-fix.css';
if (!file_exists($css_file)) {
    echo '<p style="color: red;">❌ CSS fix file not found!</p>';
    exit;
}

$css_content = file_get_contents($css_file);

// Add CSS to WordPress
$css_added = false;

// Method 1: Add to theme's style.css
$theme_dir = get_stylesheet_directory();
$theme_style = $theme_dir . '/style.css';

if (is_writable($theme_style)) {
    $current_style = file_get_contents($theme_style);
    if (strpos($current_style, 'RIDCOD Variation Images Fix') === false) {
        file_put_contents($theme_style, $current_style . "\n\n" . $css_content);
        echo '<p style="color: green;">✅ CSS added to theme style.css</p>';
        $css_added = true;
    } else {
        echo '<p style="color: orange;">⚠️ CSS already exists in theme style.css</p>';
        $css_added = true;
    }
} else {
    echo '<p style="color: orange;">⚠️ Cannot write to theme style.css</p>';
}

// Method 2: Add via WordPress customizer (if method 1 failed)
if (!$css_added) {
    $custom_css = wp_get_custom_css();
    if (strpos($custom_css, 'RIDCOD Variation Images Fix') === false) {
        wp_update_custom_css_post($custom_css . "\n\n" . $css_content);
        echo '<p style="color: green;">✅ CSS added via WordPress Customizer</p>';
        $css_added = true;
    } else {
        echo '<p style="color: orange;">⚠️ CSS already exists in WordPress Customizer</p>';
        $css_added = true;
    }
}

if ($css_added) {
    echo '<div style="background: #d4edda; border: 1px solid #c3e6cb; padding: 15px; margin: 20px 0; border-radius: 5px;">';
    echo '<h3>✅ CSS Fix Applied Successfully!</h3>';
    echo '<p>The variation images should now be visible. Please:</p>';
    echo '<ol>';
    echo '<li>Clear any caching plugins</li>';
    echo '<li>Hard refresh your browser (Ctrl+F5 or Cmd+Shift+R)</li>';
    echo '<li>Check your product page again</li>';
    echo '</ol>';
    echo '</div>';
} else {
    echo '<div style="background: #f8d7da; border: 1px solid #f5c6cb; padding: 15px; margin: 20px 0; border-radius: 5px;">';
    echo '<h3>❌ Could not add CSS automatically</h3>';
    echo '<p>Please manually add the following CSS to your theme:</p>';
    echo '<textarea style="width: 100%; height: 200px; font-family: monospace;">' . esc_textarea($css_content) . '</textarea>';
    echo '<p><strong>Where to add it:</strong></p>';
    echo '<ul>';
    echo '<li>Appearance > Customize > Additional CSS</li>';
    echo '<li>Or in your theme\'s style.css file</li>';
    echo '<li>Or in your child theme\'s style.css</li>';
    echo '</ul>';
    echo '</div>';
}

// Show current CSS content for reference
echo '<h3>CSS Fix Content:</h3>';
echo '<details>';
echo '<summary>Click to view CSS content</summary>';
echo '<pre style="background: #f8f9fa; padding: 10px; border-radius: 3px; overflow-x: auto;">';
echo esc_html($css_content);
echo '</pre>';
echo '</details>';

echo '<hr>';
echo '<h3>Troubleshooting:</h3>';
echo '<ul>';
echo '<li>If images still don\'t show, check browser developer tools (F12)</li>';
echo '<li>Look for any CSS conflicts in the Console tab</li>';
echo '<li>Make sure the images are actually loading (check Network tab)</li>';
echo '<li>Try disabling other plugins temporarily</li>';
echo '</ul>';

echo '<h3>Quick Links:</h3>';
echo '<ul>';
echo '<li><a href="' . admin_url('customize.php') . '" target="_blank">WordPress Customizer</a></li>';
echo '<li><a href="' . admin_url('theme-editor.php') . '" target="_blank">Theme Editor</a></li>';
echo '</ul>';
?>
