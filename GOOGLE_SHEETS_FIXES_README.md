# إصلاحات مشاكل Google Sheets - RIDCOD Plugin

## ملخص المشاكل المحلولة

تم حل المشاكل التالية في ميزة Google Sheets:

### 1. ✅ مشكلة رقم الهاتف - عدم ظهور الصفر في البداية

**المشكلة:** كان رقم الهاتف يفقد الرقم الأول (الصفر) عند إرساله إلى Google Sheets.

**السبب:** Google Sheets يعامل الأرقام التي تبدأ بصفر كأرقام وليس كنص، مما يؤدي إلى حذف الصفر.

**الحل المطبق:**
- إضافة علامة اقتباس (') قبل رقم الهاتف في PHP لإجبار Google Sheets على معاملته كنص
- تحديث كود Google Apps Script لضمان معالجة رقم الهاتف كنص

**الملفات المعدلة:**
- `includes/class-rid-cod-ajax.php` (السطور 486-510 و 1382-1398)
- `includes/class-rid-cod-google-sheets-settings.php` (السطور 121-133)

### 2. ✅ مشكلة الملاحظات الإضافية - عدم الإرسال

**المشكلة:** لم يتم إرسال الملاحظات الإضافية إلى Google Sheets.

**السبب:** النموذج يستخدم `name="notes"` بينما الكود كان يبحث عن `$_POST['order_notes']`.

**الحل المطبق:**
- تحديث معالجة الملاحظات في PHP للبحث عن كلا الحقلين (`notes` و `order_notes`)
- إضافة حقل الملاحظات إلى بيانات Google Sheets
- تحديث Google Apps Script لإضافة عمود الملاحظات

**الملفات المعدلة:**
- `includes/class-rid-cod-ajax.php` (السطور 307-313 و 486-518 و 1384-1401)
- `includes/class-rid-cod-google-sheets-settings.php` (السطور 66-77)

### 3. ✅ مشكلة الولايات عند التحويل إلى حقول نصية

**المشكلة:** عدم إرسال بيانات الولايات إلى Google Sheets عندما يتم تحويل الحقول من قائمة منسدلة إلى حقول نصية.

**السبب:** 
- تغيير ID الحقل من `rid-cod-state` إلى `rid-cod-state-text` عند التحويل
- الكود كان يحاول الحصول على النص من `option:selected` حتى مع الحقول النصية

**الحل المطبق:**
- الحفاظ على نفس ID (`rid-cod-state`) عند التحويل إلى حقل نصي
- تحديث JavaScript للتعامل مع كلا النوعين (select و input) بشكل صحيح
- إصلاح دالة إرسال النموذج ودالة حفظ المسودة

**الملفات المعدلة:**
- `assets/js/rid-cod.js` (السطور 1136-1145 و 218-229 و 918-926)

## التغييرات المطلوبة في Google Apps Script

يجب تحديث كود Google Apps Script في Google Apps Script Editor بالكود الجديد المحدث:

### التغييرات الرئيسية:

1. **إضافة حقل الملاحظات:**
```javascript
// العناوين الجديدة (مع إضافة الملاحظات)
const HEADERS_AR = [
  "معرف المنتج", "اسم المنتج", "الكمية", "اللون", "القياس", "العرض",
  "اسم العميل", "الهاتف", "الولاية", "البلدية", "نوع التوصيل", "الملاحظات",
  "سعر المنتج", "تكلفة الشحن", "السعر الإجمالي", "تاريخ الطلب"
];

// المفاتيح الجديدة (مع إضافة notes)
const DATA_KEYS = [
  'product_id', 'product_name', 'quantity', 'color', 'size', 'width',
  'customer_name', 'phone', 'state', 'city', 'delivery_type', 'notes',
  'product_price', 'shipping_cost', 'total_price', 'order_date'
];
```

2. **معالجة خاصة لرقم الهاتف:**
```javascript
// في دالة doPost، تم إضافة معالجة خاصة لرقم الهاتف
if (key === 'phone') {
  const phoneValue = postData.hasOwnProperty(key) ? postData[key] : '';
  return phoneValue ? String(phoneValue) : '';
}
```

## كيفية تطبيق التحديثات

### 1. تحديث Google Apps Script:
1. افتح Google Apps Script Editor
2. استبدل الكود الحالي بالكود الجديد المحدث من صفحة إعدادات Google Sheets في الإضافة
3. احفظ المشروع ونشره كتطبيق ويب جديد

### 2. اختبار الحلول:
1. تأكد من تفعيل ميزة Google Sheets في إعدادات الإضافة
2. اختبر إرسال طلب يحتوي على:
   - رقم هاتف يبدأ بصفر (مثل: 0123456789)
   - ملاحظات إضافية
   - ولاية مكتوبة يدوياً (للدول غير الجزائر)
3. تحقق من Google Sheets للتأكد من ظهور جميع البيانات بشكل صحيح

## ملاحظات مهمة

- ✅ تم الحفاظ على التوافق مع الإعدادات الحالية
- ✅ جميع التغييرات متوافقة مع الإصدارات السابقة
- ✅ تم إضافة تسجيل محسن للأخطاء (error logging)
- ✅ تم اختبار الحلول مع البيانات التجريبية

## الاختبار

يمكن اختبار الحلول باستخدام:
1. دالة الاختبار المدمجة في إعدادات Google Sheets
2. إرسال طلب حقيقي من النموذج
3. فحص سجلات الأخطاء في WordPress و Google Apps Script

---

**تاريخ الإصلاح:** 2025-08-08  
**الحالة:** مكتمل ✅  
**المطور:** Augment Agent
