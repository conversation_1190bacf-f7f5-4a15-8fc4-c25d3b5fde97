/**
 * Fix for variation images - hide color swatches when images are present
 */

jQuery(document).ready(function($) {
    console.log('RIDCOD: Fixing variation images display...');
    
    function hideColorSwatchesWithImages() {
        // Find all color options that have images
        $('.variation-option.color-option.has-image').each(function() {
            var $option = $(this);
            var $colorSwatch = $option.find('.color-swatch');
            var $imageWrapper = $option.find('.variation-image-wrapper');
            var $image = $option.find('.variation-image');
            
            console.log('RIDCOD: Processing color option with image:', $option);
            
            // Hide the color swatch
            $colorSwatch.hide();
            
            // Ensure image wrapper is visible and positioned correctly
            $imageWrapper.css({
                'display': 'block',
                'position': 'absolute',
                'top': '0',
                'left': '0',
                'right': '0',
                'bottom': '0',
                'z-index': '10',
                'border-radius': 'inherit',
                'overflow': 'hidden'
            });
            
            // Ensure image is visible and sized correctly
            $image.css({
                'display': 'block',
                'width': '100%',
                'height': '100%',
                'object-fit': 'cover',
                'object-position': 'center',
                'border-radius': 'inherit'
            });
            
            // Ensure the option container has proper positioning
            $option.css({
                'position': 'relative',
                'overflow': 'hidden'
            });
            
            console.log('RIDCOD: Fixed color option:', $option.data('value'));
        });
        
        console.log('RIDCOD: Finished fixing variation images');
    }
    
    // Run the fix immediately
    hideColorSwatchesWithImages();
    
    // Run the fix again after a short delay (in case of dynamic loading)
    setTimeout(hideColorSwatchesWithImages, 500);
    
    // Run the fix when variations change
    $(document).on('found_variation', function() {
        setTimeout(hideColorSwatchesWithImages, 100);
    });
    
    // Run the fix when RIDCOD variations are updated
    $(document).on('ridcod_variations_updated', function() {
        setTimeout(hideColorSwatchesWithImages, 100);
    });
    
    // Add some debugging CSS
    $('<style>')
        .prop('type', 'text/css')
        .html(`
            /* Force hide color swatches when images are present */
            .variation-option.color-option.has-image .color-swatch {
                display: none !important;
                visibility: hidden !important;
                opacity: 0 !important;
            }
            
            /* Force show variation images */
            .variation-option.color-option.has-image .variation-image-wrapper {
                display: block !important;
                visibility: visible !important;
                opacity: 1 !important;
                position: absolute !important;
                top: 0 !important;
                left: 0 !important;
                right: 0 !important;
                bottom: 0 !important;
                z-index: 100 !important;
                border-radius: inherit !important;
                overflow: hidden !important;
            }
            
            .variation-option.color-option.has-image .variation-image {
                display: block !important;
                visibility: visible !important;
                opacity: 1 !important;
                width: 100% !important;
                height: 100% !important;
                object-fit: cover !important;
                object-position: center !important;
                border-radius: inherit !important;
            }
            
            /* Ensure proper positioning */
            .variation-option.color-option.has-image {
                position: relative !important;
                overflow: hidden !important;
            }
        `)
        .appendTo('head');
    
    console.log('RIDCOD: Added CSS fixes for variation images');
});

// Also run when page is fully loaded
window.addEventListener('load', function() {
    setTimeout(function() {
        jQuery('.variation-option.color-option.has-image .color-swatch').hide();
        console.log('RIDCOD: Final cleanup - hidden color swatches');
    }, 1000);
});
