<?php
/**
 * Quick test for the variation images fix
 */

// Load WordPress
require_once('wp-config.php');

echo '<h1>Quick Test - Variation Images Fix</h1>';

// Enable the feature
update_option('rid_cod_show_variation_images', 'yes');
echo '<p style="color: green;">✅ Feature enabled</p>';

// Test the specific attribute name from your data
$test_attr_name = 'attribute_pa_%d8%a7%d9%84%d9%84%d9%88%d9%86';
$clean_attr_name = str_replace('attribute_', '', $test_attr_name);
$decoded_attr_name = urldecode($clean_attr_name);

echo '<h2>Attribute Name Testing:</h2>';
echo '<table border="1" cellpadding="5">';
echo '<tr><th>Type</th><th>Value</th></tr>';
echo '<tr><td>Original</td><td>' . $test_attr_name . '</td></tr>';
echo '<tr><td>Clean</td><td>' . $clean_attr_name . '</td></tr>';
echo '<tr><td>Decoded</td><td>' . $decoded_attr_name . '</td></tr>';
echo '</table>';

// Test color attribute detection
$color_attributes = array(
    'color', 'colour', 'لون', 'اللون', 
    'pa_color', 'pa_colour', 'pa_لون', 'pa_اللون',
    urlencode('لون'), urlencode('اللون'), 
    urlencode('pa_لون'), urlencode('pa_اللون'),
    '%d8%a7%d9%84%d9%84%d9%88%d9%86', // URL encoded اللون
    'pa_%d8%a7%d9%84%d9%84%d9%88%d9%86' // URL encoded pa_اللون
);

echo '<h2>Color Attribute Detection:</h2>';
$is_color_attribute = false;
$matched_attr = '';

foreach ($color_attributes as $color_attr) {
    if (stripos($clean_attr_name, $color_attr) !== false || 
        stripos($decoded_attr_name, $color_attr) !== false ||
        stripos($test_attr_name, $color_attr) !== false) {
        $is_color_attribute = true;
        $matched_attr = $color_attr;
        break;
    }
}

if ($is_color_attribute) {
    echo '<p style="color: green;">✅ Color attribute detected! Matched: ' . $matched_attr . '</p>';
} else {
    echo '<p style="color: red;">❌ Color attribute NOT detected</p>';
    echo '<p>Checking against: ' . implode(', ', $color_attributes) . '</p>';
}

// Test with sample variation data from your HTML
$sample_variation = array(
    'attributes' => array(
        'attribute_pa_%d8%a7%d9%84%d9%84%d9%88%d9%86' => '%d8%a3%d8%b2%d8%b1%d9%82'
    ),
    'image' => array(
        'src' => 'http://localhost/wordpress1/wp-content/uploads/2025/08/pngegg-1.png',
        'alt' => 'pngegg (1)'
    ),
    'variation_id' => 48
);

echo '<h2>Sample Variation Processing:</h2>';
echo '<p><strong>Variation Data:</strong></p>';
echo '<pre>' . print_r($sample_variation, true) . '</pre>';

// Simulate the processing logic
$variation_images = array();
foreach ($sample_variation['attributes'] as $attr_name => $attr_value) {
    $clean_attr_name = str_replace('attribute_', '', $attr_name);
    $decoded_attr_name = urldecode($clean_attr_name);
    
    echo '<p>Processing attribute: ' . $attr_name . '</p>';
    echo '<p>Clean: ' . $clean_attr_name . '</p>';
    echo '<p>Decoded: ' . $decoded_attr_name . '</p>';
    
    $is_color_attribute = false;
    foreach ($color_attributes as $color_attr) {
        if (stripos($clean_attr_name, $color_attr) !== false || 
            stripos($decoded_attr_name, $color_attr) !== false ||
            stripos($attr_name, $color_attr) !== false) {
            $is_color_attribute = true;
            echo '<p style="color: green;">✅ Matched color attribute: ' . $color_attr . '</p>';
            break;
        }
    }
    
    if ($is_color_attribute && !empty($attr_value) && !empty($sample_variation['image']['src'])) {
        $image_data = array(
            'src' => $sample_variation['image']['src'],
            'alt' => $sample_variation['image']['alt'],
            'variation_id' => $sample_variation['variation_id']
        );
        
        $variation_images[$attr_value] = $image_data;
        echo '<p style="color: green;">✅ Image added for: ' . $attr_value . '</p>';
        
        // Also add decoded version
        $decoded_value = urldecode($attr_value);
        if ($decoded_value !== $attr_value) {
            $variation_images[$decoded_value] = $image_data;
            echo '<p style="color: green;">✅ Image also added for decoded: ' . $decoded_value . '</p>';
        }
    }
}

echo '<h2>Final Result:</h2>';
if (!empty($variation_images)) {
    echo '<p style="color: green;">✅ Success! Found ' . count($variation_images) . ' images</p>';
    echo '<table border="1" cellpadding="5">';
    echo '<tr><th>Key</th><th>Image URL</th></tr>';
    foreach ($variation_images as $key => $data) {
        echo '<tr><td>' . $key . '</td><td>' . $data['src'] . '</td></tr>';
    }
    echo '</table>';
} else {
    echo '<p style="color: red;">❌ No images found</p>';
}

echo '<hr>';
echo '<h3>Next Steps:</h3>';
echo '<ol>';
echo '<li>If this test shows success, refresh your product page</li>';
echo '<li>Clear any caches (browser, WordPress, etc.)</li>';
echo '<li>Check the product page again</li>';
echo '</ol>';
?>
